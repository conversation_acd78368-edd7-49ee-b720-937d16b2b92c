package com.glodon.qydata.mapper.system;

import com.glodon.qydata.entity.system.CustomerExistenceIndex;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 客户数据存在索引表 Mapper 接口
 * </p>
 *
 * @<NAME_EMAIL>
 * @since 2025年8月6日09:30:19
 */
@Repository
public interface CustomerExistenceIndexMapper {

    /**
     * 根据ID查询
     * @param id 主键ID
     * @return CustomerExistenceIndex
     */
    CustomerExistenceIndex selectById(@Param("id") Long id);

    /**
     * 根据客户代码查询
     * @param customerCode 客户代码
     * @return CustomerExistenceIndex
     */
    CustomerExistenceIndex selectByCustomerCode(@Param("customerCode") String customerCode);

    /**
     * 快速判断客户代码是否存在
     * @param customerCode 客户代码
     * @return 存在返回1，不存在返回0
     */
    Integer existsByCustomerCode(@Param("customerCode") String customerCode);

    /**
     * 批量检查客户代码存在性
     * @param customerCodes 客户代码列表
     * @return Map<String, Boolean> key为客户代码，value为是否存在
     */
    List<Map<String, Object>> batchCheckExistence(@Param("customerCodes") List<String> customerCodes);

    /**
     * 插入记录
     * @param record 记录对象
     * @return 影响行数
     */
    int insert(CustomerExistenceIndex record);

    /**
     * 如果不存在则插入
     * @param customerCode 客户代码
     * @return 影响行数
     */
    int insertIfNotExists(@Param("customerCode") String customerCode);

    /**
     * 批量插入
     * @param records 记录列表
     * @return 影响行数
     */
    int batchInsert(@Param("records") List<CustomerExistenceIndex> records);

    /**
     * 根据ID更新
     * @param record 记录对象
     * @return 影响行数
     */
    int updateById(CustomerExistenceIndex record);

    /**
     * 根据客户代码更新
     * @param record 记录对象
     * @return 影响行数
     */
    int updateByCustomerCode(CustomerExistenceIndex record);

    /**
     * 根据ID删除
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据客户代码删除
     * @param customerCode 客户代码
     * @return 影响行数
     */
    int deleteByCustomerCode(@Param("customerCode") String customerCode);

    /**
     * 查询总记录数
     * @return 总记录数
     */
    Long selectCount();

    /**
     * 分页查询
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 记录列表
     */
    List<CustomerExistenceIndex> selectByPage(@Param("offset") Long offset, @Param("limit") Long limit);

}
