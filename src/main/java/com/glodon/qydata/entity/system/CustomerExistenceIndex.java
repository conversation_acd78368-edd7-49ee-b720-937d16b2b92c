package com.glodon.qydata.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * customer_existence_index - 客户数据存在索引表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("customer_existence_index")
@Schema(name = "CustomerExistenceIndex对象", description = "客户数据存在索引表-用于快速判断customer_code是否存在数据")
public class CustomerExistenceIndex {
    @Schema(name = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @Schema(name = "客户编码")
    private String customerCode;
    @Schema(name = "首次创建时间")
    private LocalDateTime tblCreateDate;
    @Schema(name = "最后更新时间")
    private LocalDateTime tblUpdateDate;
}
