CREATE TABLE `customer_existence_index`
(
    `id`              bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `customer_code`   varchar(100) NOT NULL COMMENT '客户代码',
    `tbl_create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '首次创建时间',
    `tbl_update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `customer_code` (`customer_code`) COMMENT '客户代码唯一索引'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='客户数据存在索引表-用于快速判断customer_code是否存在数据';

-- 初始化customer_existence_index表数据
-- 从多个源表中提取所有唯一的客户代码并插入到目标表中
INSERT IGNORE INTO `customer_existence_index` (`customer_code`, `tbl_create_date`, `tbl_update_date`)
SELECT DISTINCT customer_code,
                NOW() as tbl_create_date,
                NOW() as tbl_update_date
FROM (
         -- 1. tb_commonprojcategory_standards_0 表的 qy_code 字段
         SELECT qy_code as customer_code
         FROM tb_commonprojcategory_standards_0
         WHERE qy_code IS NOT NULL
           AND qy_code != ''

         UNION

         -- 2. tb_commonprojcategory_standards 表的 qy_code 字段
         SELECT qy_code as customer_code
         FROM tb_commonprojcategory_standards
         WHERE qy_code IS NOT NULL
           AND qy_code != ''

         UNION

         -- 3. zb_standards_trade 表的 customer_code 字段
         SELECT customer_code
         FROM zb_standards_trade
         WHERE customer_code IS NOT NULL
           AND customer_code != ''

         UNION

         -- 4. zb_standards_main_quantity 表的 customer_code 字段
         SELECT customer_code
         FROM zb_standards_main_quantity
         WHERE customer_code IS NOT NULL
           AND customer_code != ''

         UNION

         -- 5. zb_project_feature_standards 表的 customer_code 字段
         SELECT customer_code
         FROM zb_project_feature_standards
         WHERE customer_code IS NOT NULL
           AND customer_code != ''

         UNION

         -- 6. zb_project_feature_category_view_standards 表的 customer_code 字段
         SELECT customer_code
         FROM zb_project_feature_category_view_standards
         WHERE customer_code IS NOT NULL
           AND customer_code != ''

         UNION

         -- 7. zb_project_feature_category_view_standards_0 表的 customer_code 字段
         SELECT customer_code
         FROM zb_project_feature_category_view_standards_0
         WHERE customer_code IS NOT NULL
           AND customer_code != ''

         UNION

         -- 8. zb_standards_expression 表的 qy_code 字段
         SELECT qy_code as customer_code
         FROM zb_standards_expression
         WHERE qy_code IS NOT NULL
           AND qy_code != ''

         UNION

         -- 9. zb_standards_build_standard 表的 customer_code 字段
         SELECT customer_code
         FROM zb_standards_build_standard
         WHERE customer_code IS NOT NULL
           AND customer_code != ''

         UNION

         -- 10. zb_standards_project_info 表的 customer_code 字段
         SELECT customer_code
         FROM zb_standards_project_info
         WHERE customer_code IS NOT NULL
           AND customer_code != ''

         UNION

         -- 11. zb_standards_unit 表的 customer_code 字段
         SELECT customer_code
         FROM zb_standards_unit
         WHERE customer_code IS NOT NULL
           AND customer_code != '') AS all_customer_codes
WHERE customer_code IS NOT NULL
  AND customer_code != '';
