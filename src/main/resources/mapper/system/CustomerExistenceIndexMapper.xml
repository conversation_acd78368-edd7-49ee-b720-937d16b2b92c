<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.glodon.qydata.mapper.system.CustomerExistenceIndexMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.glodon.qydata.entity.system.CustomerExistenceIndex">
        <id column="id" property="id" />
        <result column="customer_code" property="customerCode" />
        <result column="tbl_create_date" property="tblCreateDate" />
        <result column="tbl_update_date" property="tblUpdateDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, customer_code, tbl_create_date, tbl_update_date
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM customer_existence_index
        WHERE id = #{id}
    </select>

    <!-- 根据客户代码查询 -->
    <select id="selectByCustomerCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM customer_existence_index
        WHERE customer_code = #{customerCode}
    </select>

    <!-- 快速判断客户代码是否存在 -->
    <select id="existsByCustomerCode" resultType="java.lang.Integer">
        SELECT CASE WHEN COUNT(1) > 0 THEN 1 ELSE 0 END
        FROM customer_existence_index
        WHERE customer_code = #{customerCode}
    </select>

    <!-- 批量检查客户代码存在性 -->
    <select id="batchCheckExistence" resultType="java.util.Map">
        SELECT customer_code as customerCode, 1 as exists
        FROM customer_existence_index
        WHERE customer_code IN
        <foreach collection="customerCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.glodon.qydata.entity.system.CustomerExistenceIndex" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO customer_existence_index
        (customer_code, tbl_create_date, tbl_update_date)
        VALUES
        (#{customerCode}, #{tblCreateDate}, #{tblUpdateDate})
    </insert>

    <!-- 如果不存在则插入 -->
    <insert id="insertIfNotExists">
        INSERT IGNORE INTO customer_existence_index
        (customer_code, tbl_create_date, tbl_update_date)
        VALUES
        (#{customerCode}, NOW(), NOW())
    </insert>

    <!-- 批量插入 -->
    <insert id="batchInsert">
        INSERT IGNORE INTO customer_existence_index
        (customer_code, tbl_create_date, tbl_update_date)
        VALUES
        <foreach collection="records" item="record" separator=",">
            (#{record.customerCode}, #{record.tblCreateDate}, #{record.tblUpdateDate})
        </foreach>
    </insert>

    <!-- 根据ID更新 -->
    <update id="updateById" parameterType="com.glodon.qydata.entity.system.CustomerExistenceIndex">
        UPDATE customer_existence_index
        SET
            customer_code = #{customerCode},
            tbl_update_date = #{tblUpdateDate}
        WHERE id = #{id}
    </update>

    <!-- 根据客户代码更新 -->
    <update id="updateByCustomerCode" parameterType="com.glodon.qydata.entity.system.CustomerExistenceIndex">
        UPDATE customer_existence_index
        SET
            tbl_update_date = #{tblUpdateDate}
        WHERE customer_code = #{customerCode}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteById">
        DELETE FROM customer_existence_index WHERE id = #{id}
    </delete>

    <!-- 根据客户代码删除 -->
    <delete id="deleteByCustomerCode">
        DELETE FROM customer_existence_index WHERE customer_code = #{customerCode}
    </delete>

    <!-- 查询总记录数 -->
    <select id="selectCount" resultType="java.lang.Long">
        SELECT COUNT(1) FROM customer_existence_index
    </select>

    <!-- 分页查询 -->
    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM customer_existence_index
        ORDER BY id
        LIMIT #{offset}, #{limit}
    </select>

</mapper>
